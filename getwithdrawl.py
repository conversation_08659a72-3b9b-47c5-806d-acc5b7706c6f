import selenium
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
import os

# Set up Chrome options to use existing user data
chrome_options = Options()

# Use your Chrome user data directory (adjust path if needed)
user_data_dir = os.path.expanduser(r"~\AppData\Local\Google\Chrome\User Data")
chrome_options.add_argument(f"--user-data-dir={user_data_dir}")

# Use the default profile (or specify a different profile if needed)
chrome_options.add_argument("--profile-directory=Default")

# Optional: Add other useful options
chrome_options.add_argument("--no-first-run")
chrome_options.add_argument("--no-default-browser-check")

# Set up the Chrome driver with options
service = Service(ChromeDriverManager().install())
driver = webdriver.Chrome(service=service, options=chrome_options)

# Navigate to the MEXC website
driver.get('https://www.mexc.com/')

# Wait for the login button to be clickable and click it
login_button = WebDriverWait(driver, 10).until(
    EC.element_to_be_clickable((By.XPATH, '//button[@data-test-id="login-button"]'))
)
login_button.click()

