# DO NOT EDIT THIS FILE!
#
# This file is generated from the CDP specification. If you need to make
# changes, edit the generator and regenerate all of the modules.
#
# CDP domain: PWA (experimental)
from __future__ import annotations
from .util import event_class, T_JSON_DICT
from dataclasses import dataclass
import enum
import typing
from . import target


@dataclass
class FileHandlerAccept:
    '''
    The following types are the replica of
    https://crsrc.org/c/chrome/browser/web_applications/proto/web_app_os_integration_state.proto;drc=9910d3be894c8f142c977ba1023f30a656bc13fc;l=67
    '''
    #: New name of the mimetype according to
    #: https://www.iana.org/assignments/media-types/media-types.xhtml
    media_type: str

    file_extensions: typing.List[str]

    def to_json(self):
        json = dict()
        json['mediaType'] = self.media_type
        json['fileExtensions'] = [i for i in self.file_extensions]
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            media_type=str(json['mediaType']),
            file_extensions=[str(i) for i in json['fileExtensions']],
        )


@dataclass
class FileHandler:
    action: str

    accepts: typing.List[FileHandlerAccept]

    display_name: str

    def to_json(self):
        json = dict()
        json['action'] = self.action
        json['accepts'] = [i.to_json() for i in self.accepts]
        json['displayName'] = self.display_name
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            action=str(json['action']),
            accepts=[FileHandlerAccept.from_json(i) for i in json['accepts']],
            display_name=str(json['displayName']),
        )


class DisplayMode(enum.Enum):
    '''
    If user prefers opening the app in browser or an app window.
    '''
    STANDALONE = "standalone"
    BROWSER = "browser"

    def to_json(self):
        return self.value

    @classmethod
    def from_json(cls, json):
        return cls(json)


def get_os_app_state(
        manifest_id: str
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.Tuple[int, typing.List[FileHandler]]]:
    '''
    Returns the following OS state for the given manifest id.

    :param manifest_id: The id from the webapp's manifest file, commonly it's the url of the site installing the webapp. See https://web.dev/learn/pwa/web-app-manifest.
    :returns: A tuple with the following items:

        0. **badgeCount** - 
        1. **fileHandlers** - 
    '''
    params: T_JSON_DICT = dict()
    params['manifestId'] = manifest_id
    cmd_dict: T_JSON_DICT = {
        'method': 'PWA.getOsAppState',
        'params': params,
    }
    json = yield cmd_dict
    return (
        int(json['badgeCount']),
        [FileHandler.from_json(i) for i in json['fileHandlers']]
    )


def install(
        manifest_id: str,
        install_url_or_bundle_url: typing.Optional[str] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Installs the given manifest identity, optionally using the given install_url
    or IWA bundle location.

    TODO(crbug.com/337872319) Support IWA to meet the following specific
    requirement.
    IWA-specific install description: If the manifest_id is isolated-app://,
    install_url_or_bundle_url is required, and can be either an http(s) URL or
    file:// URL pointing to a signed web bundle (.swbn). The .swbn file's
    signing key must correspond to manifest_id. If Chrome is not in IWA dev
    mode, the installation will fail, regardless of the state of the allowlist.

    :param manifest_id:
    :param install_url_or_bundle_url: *(Optional)* The location of the app or bundle overriding the one derived from the manifestId.
    '''
    params: T_JSON_DICT = dict()
    params['manifestId'] = manifest_id
    if install_url_or_bundle_url is not None:
        params['installUrlOrBundleUrl'] = install_url_or_bundle_url
    cmd_dict: T_JSON_DICT = {
        'method': 'PWA.install',
        'params': params,
    }
    json = yield cmd_dict


def uninstall(
        manifest_id: str
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Uninstalls the given manifest_id and closes any opened app windows.

    :param manifest_id:
    '''
    params: T_JSON_DICT = dict()
    params['manifestId'] = manifest_id
    cmd_dict: T_JSON_DICT = {
        'method': 'PWA.uninstall',
        'params': params,
    }
    json = yield cmd_dict


def launch(
        manifest_id: str,
        url: typing.Optional[str] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,target.TargetID]:
    '''
    Launches the installed web app, or an url in the same web app instead of the
    default start url if it is provided. Returns a page Target.TargetID which
    can be used to attach to via Target.attachToTarget or similar APIs.

    :param manifest_id:
    :param url: *(Optional)*
    :returns: ID of the tab target created as a result.
    '''
    params: T_JSON_DICT = dict()
    params['manifestId'] = manifest_id
    if url is not None:
        params['url'] = url
    cmd_dict: T_JSON_DICT = {
        'method': 'PWA.launch',
        'params': params,
    }
    json = yield cmd_dict
    return target.TargetID.from_json(json['targetId'])


def launch_files_in_app(
        manifest_id: str,
        files: typing.List[str]
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.List[target.TargetID]]:
    '''
    Opens one or more local files from an installed web app identified by its
    manifestId. The web app needs to have file handlers registered to process
    the files. The API returns one or more page Target.TargetIDs which can be
    used to attach to via Target.attachToTarget or similar APIs.
    If some files in the parameters cannot be handled by the web app, they will
    be ignored. If none of the files can be handled, this API returns an error.
    If no files are provided as the parameter, this API also returns an error.

    According to the definition of the file handlers in the manifest file, one
    Target.TargetID may represent a page handling one or more files. The order
    of the returned Target.TargetIDs is not guaranteed.

    TODO(crbug.com/339454034): Check the existences of the input files.

    :param manifest_id:
    :param files:
    :returns: IDs of the tab targets created as the result.
    '''
    params: T_JSON_DICT = dict()
    params['manifestId'] = manifest_id
    params['files'] = [i for i in files]
    cmd_dict: T_JSON_DICT = {
        'method': 'PWA.launchFilesInApp',
        'params': params,
    }
    json = yield cmd_dict
    return [target.TargetID.from_json(i) for i in json['targetIds']]


def open_current_page_in_app(
        manifest_id: str
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Opens the current page in its web app identified by the manifest id, needs
    to be called on a page target. This function returns immediately without
    waiting for the app to finish loading.

    :param manifest_id:
    '''
    params: T_JSON_DICT = dict()
    params['manifestId'] = manifest_id
    cmd_dict: T_JSON_DICT = {
        'method': 'PWA.openCurrentPageInApp',
        'params': params,
    }
    json = yield cmd_dict


def change_app_user_settings(
        manifest_id: str,
        link_capturing: typing.Optional[bool] = None,
        display_mode: typing.Optional[DisplayMode] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Changes user settings of the web app identified by its manifestId. If the
    app was not installed, this command returns an error. Unset parameters will
    be ignored; unrecognized values will cause an error.

    Unlike the ones defined in the manifest files of the web apps, these
    settings are provided by the browser and controlled by the users, they
    impact the way the browser handling the web apps.

    See the comment of each parameter.

    :param manifest_id:
    :param link_capturing: *(Optional)* If user allows the links clicked on by the user in the app's scope, or extended scope if the manifest has scope extensions and the flags ```DesktopPWAsLinkCapturingWithScopeExtensions```` and ````WebAppEnableScopeExtensions``` are enabled.  Note, the API does not support resetting the linkCapturing to the initial value, uninstalling and installing the web app again will reset it.  TODO(crbug.com/339453269): Setting this value on ChromeOS is not supported yet.
    :param display_mode: *(Optional)*
    '''
    params: T_JSON_DICT = dict()
    params['manifestId'] = manifest_id
    if link_capturing is not None:
        params['linkCapturing'] = link_capturing
    if display_mode is not None:
        params['displayMode'] = display_mode.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'PWA.changeAppUserSettings',
        'params': params,
    }
    json = yield cmd_dict
